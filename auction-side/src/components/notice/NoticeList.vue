<script setup>
import { computed, onBeforeMount, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useLocale } from 'vuetify'
import useNotice from '../../composables/useNotice'
import { PATH_NAME } from '../../defined/const'

const route = useRoute()
const router = useRouter()

defineProps({
  showMore: {
    type: Boolean,
    default: true
  }
})

// 緊急以外お知らせ
const { getNotices, notices } = useNotice()
const { t: translate, current } = useLocale()

const reload = () => {
  getNotices({
    limit: null
  })
}

watch(
  () => current.value,
  () => {
    reload()
  }
)

const noticeList = computed(() => {
  if (route.path === PATH_NAME.TOP) {
    return notices.filter((x) => x.display_code !== 3)
  }
  if (route.path === PATH_NAME.NOTICE_LIST) {
    return notices.filter((x) => x.display_code !== 3)
  }
  if (route.path === PATH_NAME.NOTICE_LIST_IMPORTANT) {
    return notices.filter((x) => x.display_code === 3)
  }
  return []
})

onBeforeMount(() => {
  getNotices({
    limit: null
  })
})
</script>
<template>
  <section id="info" class="">
    <div class="container">
      <div id="infoNotice" class="info-box-notice">
        <h2>{{ translate('notice.noticeList') }}</h2>

        <v-list class="mt-5">
          <v-list-item
            v-for="notice in noticeList"
            :key="notice.title"
            @click="router.push(`${PATH_NAME.NOTICE_LIST}/${notice.notice_no}`)"
          >
            <template v-slot:prepend>
              <div style="width: 40px">
                <div v-if="notice.is_new" class="text-red text-subtitle font-weight-bold">NEW</div>
              </div>
            </template>

            <v-list-item-title class="d-flex flex-row align-center ga-3">
              <div class="text-h6 ml-5">{{ notice.create_date }}</div>
              <div class="text-h6 ml-5 v-list-title">{{ notice.title }}</div>
            </v-list-item-title>
          </v-list-item>
        </v-list>

        <div class="newslist-more" v-if="showMore">
          <button class="btn" @click="reload">
            <span class="txt">{{ translate('notice.more') }}<span class="arrow"></span></span>
          </button>
        </div>
      </div>
    </div>
  </section>
</template>
<style scoped>
#info {
  padding: 20px 1rem !important;
}
</style>
<style scoped>
/* Normal state */
.custom-link {
  color: black; /* Set the initial text color */
  text-decoration: none; /* Remove default underline */
}

/* Hover state */
.custom-link:hover {
  color: blue; /* Change text color on hover */
  text-decoration: underline; /* Add underline on hover */
}
.v-list-title {
  white-space: normal;
  overflow: hidden;
  width: 100%;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
</style>
